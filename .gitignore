# Dependencies
node_modules/

# Build outputs
dist/
build/

# Development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# Package tarballs
*.tgz

# Test files
*.test.js
*.spec.js

# AI
CLAUDE.md
.claude
agent_ideas.md
.serena

# Tests
local_testing.md