{"extends": "../../../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES2020"], "module": "ESNext", "moduleResolution": "bundler", "outDir": "../public/dist", "rootDir": "../../", "strict": true, "strictNullChecks": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "sourceMap": true, "declaration": false, "declarationMap": false, "noEmit": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "incremental": true, "tsBuildInfoFile": "../public/dist/.tsbuildinfo"}, "include": ["../client/**/*", "../shared/**/*", "../tunnel/types.ts", "../parser.ts", "../../steering.ts"], "exclude": ["node_modules", "../public/dist", "**/*.test.ts", "server.ts", "cli.ts", "watcher.ts"]}