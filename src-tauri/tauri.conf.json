{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "claude-dashboard", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "..", "devUrl": "http://localhost:8247", "beforeDevCommand": "pnpm run dev:dashboard:tauri", "beforeBuildCommand": "pnpm build"}, "app": {"windows": [{"title": "<PERSON>", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}