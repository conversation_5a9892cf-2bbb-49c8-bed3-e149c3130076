# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.5.8] - 2025-08-14

- Dashboard improvements for multi-project support
- Bug tracking system enhancements
- Fixed issue where deleted spec directories remained in dashboard
- Performance optimizations
- Various bug fixes and stability improvements

**Note:** This is the last release of the claude-code-spec-workflow. Development focus has shifted to the MCP version of this workflow system. The MCP version provides enhanced features, real-time dashboard, and broader AI tool compatibility.

## [1.5.7] - 2025-08-07

### Updated
- Updated README.

## [1.5.6] - 2025-08-05

### Changed
- Removed all other sub agents due to overcomplication of workflow.
  - Kept the following sub agents:
    - spec-task-executor
    - spec-requirements-validator
    - spec-design-validator
    - spec-task-validator
- Added a few more tools for retrieving specific context documents with added in memory caching for 1 hour.
- Removed spec-orchestrator command as it did not work as expected especially due to claude code's auto compacting of commands and sometimes the agent would just stop for no reason.
- On .claude folder detection, the folder will be backuped and a fresh install will be performed, you will need to manually move any custom commands / sub agents you have created to the new .claude folder. The setup script will attempt to migrate all other files such as specs, steering documents, bugs and task commands.
- Overall workflow has been simplified and made more robust.

### Fixed
- Improved bug status determination to prevent premature 'verifying' state


## [1.5.5] - 2025-08-02

### Fixed
- Automated Task Completion: Updated all task completion instructions across agents and commands to use `get-tasks --mode complete` script instead of manual tasks.md editing for consistent cross-platform behavior

## [1.5.4] - 2025-08-02

### Fixed
1. Fixed Regex Global Flag Issues: Added lastIndex = 0 resets to prevent state pollution between tests
2. Updated Conditional Language Regex: Enhanced pattern matching for agent usage checks
3. Standardized Get-Content Usage: Replaced all hardcoded file paths with proper cross-platform get-content script examples
4. Fixed Unused Variable: Cleaned up lint error in src/update.ts
5. Updated Parser Test Expectations: Aligned tests with new parser behavior that includes all requirement types

## [1.5.3] - 2025-08-02

### Added

## [1.5.2] - 2025-08-02

### Fixed
- The { isDefault: true } flag in Commander.js was too aggressive in matching - it would route ANY unmatched input to the default command, even when that input was actually meant to be a different command that Commander.js failed to parse correctly in certain environments.

## [1.5.1] - 2025-08-02

### Fixed
- Removed the custom argument handling that was interfering with command routing

## [1.5.0] - 2025-08-02

### Fixed
- Minor tweaks to sub agent calls especially around script command calls.
- Tweaks to CLI which should fix issues in Linux, WSL and MacOS with calling npx @pimzino/claude-code-spec-workflow@latest commands.

## [1.4.9] - 2025-08-02

### Fixed
- Fixed critical build process bug where markdown command files were not being included in published packages

## [1.4.8] - 2025-08-02

### Fixed
- Replaced all instances of npx @pimzino\claude-code-spec-workflow with npx @pimzino/claude-code-spec-workflow@latest to avoid cache issues.

## [1.4.7] - 2025-08-02

### Fixed
- Fixed a bug with the task command regeneration when running an update to a current project with the workflow already in use.

## [1.4.6] - 2025-08-02

### Added
- Implemented various scripts for the agent/s to call which should improve context management (tasks.md doc no longer loaded into context, only the relevant task is. This is only valid for some scenarios), agent token waste reduction, and task management (tasks.md).

### Enhancements
- Improved workflow update process for projects where the workflow has been used previously.
- Agents, Commands, Templates have all been removed from functions and are now markdown documents in the repository /src/markdown. This should make it easier to modify / update in future as well as allow anyone to suggest changes or read the agents / commands directly in the repo without having to read a ton of typescript in between.

### Fixed
- Fixed ESLint warnings in dashboard parser (unused variables in catch blocks)

## [1.4.4] - 2025-08-01

### Added
  - Stateless design using tasks.md as single source of truth

### Fixed
- README documentation has been simplified.

## [1.5.0] - 2025-01-05

### Added
- **Dashboard Tunnel Feature**: Secure, temporary sharing of dashboard with external stakeholders
  - New `--tunnel` flag for `claude-spec-dashboard` command creates HTTPS URLs
  - Optional password protection with `--tunnel-password` for access control
  - Multiple tunnel providers (Cloudflare, ngrok) with automatic fallback
  - Native Node.js bindings for ngrok for improved reliability
  - Read-only access enforcement for all external viewers
  - Real-time usage analytics tracking visitor count and access times
  - Visual tunnel status display in dashboard UI
  - Comprehensive error handling and provider failover
  - Zero configuration required - works out of the box
  - Automatic ngrok auth token detection from local config
  - Cross-platform executable permissions fix

### Enhanced
- **Security Architecture**: Implemented multi-layer security for tunnel access
  - All tunnel traffic encrypted via HTTPS/WSS
  - Read-only middleware blocks all write operations
  - WebSocket message filtering for read-only mode
  - Rate limiting on password authentication attempts
  - Session management for authenticated users

### Technical Implementation
- Created modular tunnel system in `src/dashboard/tunnel/`
  - `TunnelManager` orchestrates tunnel lifecycle
  - Provider abstraction supports multiple tunnel services
  - Native ngrok implementation using `@ngrok/ngrok` package
  - Cloudflare provider using `cloudflared` CLI
  - `AccessController` enforces read-only access
  - `UsageTracker` collects privacy-preserving analytics
  - Comprehensive test coverage for all tunnel components
  - Example scripts for common use cases (team standups, client demos, etc.)

### Documentation
- Created comprehensive tunnel documentation in `docs/tunnel-feature.md`
- Added tunnel examples in `examples/tunnel/` directory
- Updated README with tunnel feature section
- Created troubleshooting guide for common tunnel issues

## [1.4.2] - 2025-07-30

### Updated
- Updated documentation in cli.js and README to reflect current claude code slash command list

## [1.4.1] - 2025-07-30

### 🔧 Changed
- **Streamlined Commands**: Removed redundant `/spec-requirements`, `/spec-design`, and `/spec-tasks` commands - `/spec-create` now handles the complete workflow

## [1.4.0] - 2025-07-30 🚀

### Added - Claude Code Sub-Agents Revolution!
- **Intelligent Quality Gates**: Introduced 4 powerful sub-agents for automated validation
  - `spec-task-executor` - Specialized implementation agent for maximum context and flawless task execution
  - `spec-requirements-validator` - Rigorous requirements quality validation with template compliance
  - `spec-design-validator` - Comprehensive design validation with cross-phase requirement coverage
  - `spec-task-validator` - Atomic task validation ensuring 90%+ agent success rates

### Enhanced - Atomic Task Engineering
- **Atomic Task Criteria**: Revolutionary task granularity with 1-3 file scope, 15-30 minute timeboxing
- **Agent-Friendly Design**: Tasks engineered for autonomous execution without human intervention
- **Cross-Phase Validation**: Validators now ensure complete traceability across Requirements → Design → Tasks
- **Template Structure Validation**: Every document validated against official templates for consistency

### Changed - Priority-Based Execution
- **Smart Agent Detection**: Automatic fallback from agent-based to manual execution
- **Optional Agent Setup**: User choice during installation - no forced agent adoption
- **Validation-First Workflow**: Documents automatically validated before user review
- **Tool Restriction Removal**: Agents now have access to all tools with explicit behavioral constraints

This is a **game-changing update** that transforms spec-driven development with intelligent automation!

## [1.3.6] - 2025-07-29

### Fixed
- Fix dashboard issues: active sessions updates and spec display names

## [1.3.5] - 2025-07-29

### Enhanced
- **Command Workflow Clarity**: Consolidated all workflow instructions into single `spec-create` command for better agent guidance
  - **Root Cause**: Individual spec commands (`/spec-requirements`, `/spec-design`, `/spec-tasks`) cannot be executed by Claude Code, causing agent confusion
  - **Primary Fix**: Removed all confusing command references from workflow instructions
  - **Secondary Fix**: Enhanced `spec-create` command with complete Requirements → Design → Tasks → Commands workflow
  - **Template Integration**: All commands now reference actual template files instead of inline examples
  - **Result**: Agents receive clear, actionable guidance without attempting to execute unavailable commands

- **Template Content Completeness**: Enhanced all templates with comprehensive guidance and examples
  - Added `_Leverage:` examples throughout task template showing code reuse opportunities
  - Enhanced design template with code reuse analysis and integration points sections
  - Added task format guidelines and detailed examples to task template
  - **Result**: Templates are now self-contained with all necessary guidance for each workflow phase

- **Bug Workflow Consistency**: Applied same clarity improvements to all bug fix commands
  - Removed inline template examples from `bug-create`, `bug-analyze`, and `bug-verify` commands
  - Added explicit template file references for `bug-report-template.md`, `bug-analysis-template.md`, and `bug-verification-template.md`
  - **Result**: Consistent approach across both spec and bug workflows with clean, focused commands

## [1.3.4] - 2025-07-28

### Fixed
- **Real-time Steering Document Detection**: Fixed GitHub issue #19 where dashboard wouldn't detect steering document changes in real-time
  - **Root Cause**: Dashboard file watcher only monitored `.claude/specs` but not `.claude/steering` directory
  - **Primary Fix**: Extended `SpecWatcher` class to monitor steering directory and emit `steering-change` events
  - **Secondary Fix**: Added robust path normalization to handle URL encoding/decoding and cross-platform path differences
  - **Dashboard Updates**: Enhanced both single-project and multi-project dashboard servers to handle steering events
  - **Frontend Integration**: Updated JavaScript to handle real-time steering status updates via WebSocket
  - **Result**: Users now see immediate feedback when running `/spec-steering-setup` without manual page refresh

### Enhanced
- **Cross-platform Path Handling**: Improved path normalization for Windows, macOS, and Linux compatibility
  - Added `resolve()` and `normalize()` to `SpecParser` constructor for consistent path handling
  - Enhanced multi-project dashboard to handle URL-encoded paths with spaces and special characters
  - Fixed path separator inconsistencies between different operating systems
  - **Result**: Dashboard works reliably across all platforms with various project path formats

### Added
- **Comprehensive Path Testing**: Added extensive test coverage for path normalization scenarios
  - New `parser.test.ts` with 7 path normalization test cases
  - Enhanced `steering.test.ts` with cross-platform path compatibility tests
  - Coverage for URL encoding, path separators, trailing slashes, and spaces in paths
  - **Result**: 55 total tests passing, ensuring robust path handling

## [1.3.3] - 2025-07-27

### Fixed
- **Dashboard Compatibility**: Fixed Fastify version compatibility issue in dashboard
  - Downgraded `@fastify/static` from v8.2.0 to v7.0.4 to maintain compatibility with Fastify v4.24.3
  - **Result**: Dashboard now starts correctly without version mismatch errors

## [1.3.2] - 2025-07-27

### Fixed
- **Deprecated Dependency Warnings**: Eliminated all deprecation warnings during package installation
  - Updated `@fastify/static` from v6.12.0 to v8.2.0 (eliminates old glob dependency)
  - Updated `commander` from v11.1.0 to v12.1.0
  - Replaced `cpx2` with `cpy-cli` to eliminate deprecated inflight and glob dependencies
  - Updated all dev dependencies to latest versions
  - **Result**: Clean installation with no more "npm warn deprecated" messages

- **Task Command Generation Reliability**: Fixed issue where task parsing would fail with format variations
  - Enhanced task parser to handle flexible spacing, punctuation, and formatting
  - Added support for inline metadata extraction (_Requirements:_, _Leverage:_)
  - Improved multi-line task description handling
  - Added graceful handling of format variations agents naturally produce
  - **Result**: Task command generation now works reliably regardless of minor formatting differences

### Enhanced
- **Comprehensive Test Coverage**: Added extensive unit tests for task parsing edge cases
  - Tests for format variations (with/without periods, extra spaces, inline metadata)
  - Tests for deeply nested task hierarchies (******* etc.)
  - Tests for mixed formats within same document
  - All 45 tests passing with robust coverage

- **Template Consistency**: Updated task templates and command instructions
  - Reinforced correct format through positive examples only (avoiding "pink elephant" effect)
  - Enhanced format rules with clear, consistent examples
  - Improved task format documentation in command instructions

### Technical Details
- **ESLint Migration**: Updated from ESLint v8 to v9 with new flat config format
- **Dependency Updates**: All dependencies updated to latest stable versions
- **Code Quality**: Fixed linting issues and improved error handling
- **Build Process**: Updated copy command to use `cpy-cli` instead of deprecated `cpx2`

## [1.3.1] - 2025-07-27

### Fixed
- **spec-execute Command Path Issue**: Fixed issue where `/spec-execute` command couldn't find spec files after `/clear` by adding explicit paths to `.claude/specs/{feature-name}/` in command instructions
- **Dashboard npx Command**: Updated README to use correct npx command format (`npx -p @pimzino/claude-code-spec-workflow claude-spec-dashboard`) to resolve package not found errors

### Changed
- Updated `/spec-requirements`, `/spec-design`, and `/spec-tasks` commands to include explicit file paths for better clarity

## [1.3.0] - 2025-07-26

### Added
- **Bug Fix Workflow**: Complete 5-command workflow for systematic bug handling
  - New `/bug-report`, `/bug-analyze`, `/bug-fix`, `/bug-test`, `/bug-verify` commands
  - Comprehensive templates for bug reporting, analysis, and verification
  - Streamlined process for identifying, fixing, and validating bug fixes
- **Real-Time Dashboard**: Web-based monitoring dashboard for specs and tasks
  - `claude-spec-dashboard` command launches a lightweight Fastify server
  - Built with petite-vue (6kb) and Tailwind CSS for optimal performance
  - Real-time updates via WebSocket when specs/tasks change
  - Visual progress tracking with expandable task lists
  - Shows task leverage references and requirements
  - Git branch and commit information display
  - Steering documents status integration with visual indicators
- **Dynamic Version Management**: CLI now dynamically reads version from package.json with fallback handling

### Changed
- **Architecture Simplification**: Removed CLAUDE.md generation and integrated workflow instructions directly into individual command files
  - Streamlined documentation structure and setup process
  - Enhanced command organization and maintainability
- **Documentation Updates**: Updated README and CLI output for NPX-based task command generation approach

### Enhanced
- **Dashboard Display**: Improved spec display name extraction to check all document types for better presentation
- **Steering Documents Integration**: Added steering documents status to dashboard with prompts to run `/spec-steering-setup` when missing

## [1.2.5] - 2025-07-24

### Changed
- **Replaced Script-Based Task Command Generation**: Removed finicky platform-specific scripts (`.bat`, `.sh`, launcher) in favor of clean NPX command
  - New approach: `npx @pimzino/claude-code-spec-workflow@latest generate-task-commands {spec-name}`
  - Added user choice: Agent now asks "Would you like me to generate individual task commands? (yes/no)"
  - Cross-platform compatibility: Works automatically on Windows, macOS, and Linux
  - Cleaner workflow: No more script maintenance or platform detection issues

### Added
- **New CLI Command**: Added `generate-task-commands` command to the package CLI
- **Task Parsing Logic**: Implemented robust task parsing that correctly handles the template format
- **User-Friendly Approach**: Optional task command generation based on user preference

### Technical Details
- Added `src/task-generator.ts` with parsing and generation logic
- Updated `src/cli.ts` with new `generate-task-commands` command
- Updated `src/claude-md.ts` workflow instructions to use NPX approach
- Updated `src/commands.ts` slash command definitions to use new workflow
- Maintains backward compatibility with traditional `/spec-execute` approach

## [1.2.4] - 2025-07-22

### Added
- **Steering Documents Feature**: Integrated persistent project context inspired by Kiro's steering concept
  - New `/spec-steering-setup` command to create and manage steering documents
  - Three steering document templates: `product.md`, `tech.md`, and `structure.md`
  - Steering document loader utility for seamless integration
  - New `.claude/steering/` directory for persistent project context
  - All spec commands now reference and utilize steering documents when available

### Enhanced
- **Language-Agnostic Templates**: Refactored all templates to be truly language and project-type agnostic
  - Removed TypeScript-specific syntax from design templates
  - Made tech template generic for all project types (CLI tools, desktop apps, libraries, etc.)
  - Updated structure template with flexible examples instead of web-centric assumptions
  - Templates now work for any programming language or project type

### Technical Details
- Added `src/steering.ts` with `SteeringLoader` class for document management
- Updated all command functions to reference steering documents
  - `/spec-create` loads and aligns with product vision
  - `/spec-requirements` references product.md for alignment
  - `/spec-design` follows tech.md and structure.md patterns
  - `/spec-tasks` respects structure.md conventions
  - `/spec-execute` implements following all steering guidelines
- Added comprehensive test coverage for steering features
- Fixed template string escaping issues in `claude-md.ts`
- All 35 tests passing including new steering and template tests

## [1.2.3] - 2025-07-22

### Added
- **Code Reuse First Approach**: Enhanced workflow to prioritize analyzing existing codebase and leveraging existing components
  - Added mandatory codebase exploration step in `/spec-create` command before writing requirements
  - Enhanced `/spec-design` command with specific codebase research phase and "Code Reuse Analysis" section
  - Improved `/spec-tasks` command to prioritize extending/adapting existing code over building from scratch
  - New `_Leverage:` format in task definitions to reference specific existing code alongside `_Requirements:`
  - Updated core principles in CLAUDE.md template to include "Code Reuse First"

### Enhanced
- **Command Generation Scripts**: Updated both Windows (.bat) and Unix (.sh) scripts to parse and include `_Leverage:` information
  - Generated task commands now include "Code Reuse" sections when leverage information is present
  - Enhanced command templates emphasize using existing components and utilities
  - Improved task execution guidance to prioritize leveraging existing code

## [1.2.2] - 2025-07-20

### Fixed
- **ESM Compatibility Issue**: Fixed `ERR_REQUIRE_ESM` error when running `npx @pimzino/claude-code-spec-workflow`
  - Downgraded `chalk` from `^5.3.0` to `^4.1.2` (last CommonJS-compatible version)
  - Downgraded `inquirer` from `^9.2.12` to `^8.2.6` (last CommonJS-compatible version)
  - Downgraded `ora` from `^7.0.1` to `^5.4.1` (last CommonJS-compatible version)
  - Updated `@types/inquirer` to match inquirer version `^8.2.10`
  - This resolves the common installation error users were experiencing

### Added
- **Restart Notification**: Added prominent notifications in command generation scripts to inform users they need to restart Claude Code for new task commands to be visible
  - Updated both Windows (`generate-commands.bat`) and Unix (`generate-commands.sh`) scripts
  - Added restart instructions to workflow documentation and agent guidance
  - Ensures users know to restart Claude Code after task command generation

## [1.2.1] - 2025-07-19

### Fixed
- **Windows Script Parsing**: Fixed task parsing logic in Windows batch script
  - Replaced complex regex patterns with arithmetic validation for task IDs
  - Fixed parsing of hierarchical task numbering (1, 2.1, 2.2, etc.)
  - Removed Unicode emoji characters for clean, professional output
  - Ensured proper task detection using simplified string matching

## [1.2.0] - 2025-07-19

### Added
- **Platform-Specific Command Generation**: Replaced JavaScript-based script with platform-specific alternatives
  - Added `generate-commands.bat` for Windows systems with native batch scripting
  - Added `generate-commands.sh` for macOS and Linux systems with bash scripting
  - Added `generate-commands-launcher.sh` for automatic OS detection and script selection
  - Added comprehensive script documentation in `.claude/scripts/README.md`

### Changed
- **Cross-Platform Compatibility**: Eliminated Node.js dependency for command generation
  - Agents now use `./.claude/scripts/generate-commands-launcher.sh {spec-name}` instead of `node .claude/scripts/generate-commands.js {spec-name}`
  - Automatic OS detection ensures the correct script runs on each platform (Windows/macOS/Linux)
  - Maintains all existing functionality: task parsing, hierarchical numbering, command file generation
  - Updated all workflow documentation and command references

### Improved
- **Conflict Prevention**: Platform-specific scripts avoid potential JavaScript conflicts
  - No more Node.js version compatibility issues or runtime conflicts
  - Reduced dependency on external runtime environments
  - Better integration with native shell environments
  - Faster execution using native platform commands

### Deprecated
- **JavaScript Command Generation**: The `generate-commands.js` script is no longer created
  - Legacy function `getCommandGenerationScript()` now returns migration instructions
  - All documentation updated to reference new platform-specific approach
  - Clear deprecation warnings guide users to new system

### Fixed
- **Windows Script Parsing**: Fixed task parsing logic in Windows batch script
  - Replaced complex regex patterns with arithmetic validation for task IDs
  - Fixed parsing of hierarchical task numbering (1, 2.1, 2.2, etc.)
  - Removed Unicode emoji characters for clean, professional output
  - Ensured proper task detection using simplified string matching

### Technical Details
- **Setup Process**: Modified `createScripts()` to generate four files (Windows, Unix, launcher, documentation)
- **File Permissions**: Automatically sets execute permissions for shell scripts on Unix-like systems
- **Error Handling**: Graceful OS detection with clear error messages for unsupported platforms
- **Test Coverage**: Updated test suite to verify all platform-specific scripts are created correctly
- **Backward Compatibility**: Smooth transition with comprehensive migration documentation
- **Windows Compatibility**: Improved batch script reliability with simplified validation logic

## [1.1.2] - 2025-07-19

### Fixed
- **Workflow Sequence Clarity**: Clarified exact workflow sequence to prevent script execution at wrong times
  - Updated `/spec-create` to explicitly state "DO NOT run scripts during requirements phase"
  - Updated `/spec-tasks` to clearly indicate script should ONLY run after tasks approval
  - Added prominent workflow sequence documentation: Requirements → Design → Tasks → Generate Commands
  - Added explicit warnings against editing the command generation script

### Improved
- **Agent Instruction Clarity**: Eliminated confusion about when to run the command generation script
  - Removed premature script execution instructions from requirements phase
  - Added clear "DO NOT" statements to prevent early script execution
  - Specified exact timing: "ONLY after tasks are approved"
  - Added warnings against script modification attempts

### Documentation
- **Workflow Sequence**: Added clear step-by-step workflow sequence in CLAUDE.md
- **Script Usage Rules**: Added dedicated section explaining proper script usage
- **Timing Clarity**: Made it crystal clear that scripts run ONLY after tasks approval
- **Anti-Editing Warnings**: Added explicit instructions not to modify the generation script

### Technical Details
- Updated `src/commands.ts` with corrected workflow sequence for both `/spec-create` and `/spec-tasks`
- Enhanced `src/claude-md.ts` with prominent workflow sequence and script usage rules
- Removed confusing early script execution instructions from requirements phase
- Added multiple reinforcement points about proper script timing and usage

## [1.1.1] - 2025-07-19

### Fixed
- **Command Generation Instructions**: Made script execution mandatory and explicit in workflow commands
  - Updated `/spec-create` command with prominent "MUST EXECUTE" instructions for script execution
  - Updated `/spec-tasks` command with explicit "ACTION REQUIRED" language for script execution
  - Added clear warnings at the top of command instructions about script execution requirement
  - Enhanced CLAUDE.md documentation with explicit timing for when to run the script

### Improved
- **Agent Compliance**: Agents now receive clear, mandatory instructions to execute the command generation script
  - Changed passive language ("Run the script") to active mandatory language ("MUST EXECUTE")
  - Added prominent placement of script execution instructions at the top of commands
  - Specified exact timing: "immediately after requirements approval" and "immediately after tasks approval"
  - Multiple reinforcement points ensure agents understand the requirement

### Technical Details
- Updated `src/commands.ts` with explicit script execution instructions in `/spec-create` and `/spec-tasks`
- Enhanced `src/claude-md.ts` with clear workflow steps including mandatory script execution
- Added "REQUIRED after approval" sections with action-oriented language
- Maintained backward compatibility while improving agent instruction clarity

## [1.1.0] - 2025-07-19

### Added
- **Auto-Generated Task Commands**: Individual commands are now automatically created for each task in a spec
  - Commands like `/user-auth-task-1`, `/user-auth-task-2.1` are generated automatically
  - Each command calls `/spec-execute {task-id} {spec-name}` with proper parameters
  - Commands are organized in spec-specific folders: `.claude/commands/{spec-name}/`
- **Command Generation Script**: New script at `.claude/scripts/generate-commands.js`
  - Parses `tasks.md` files and creates individual task commands
  - Supports hierarchical task numbering (1, 2, 2.1, 2.2, etc.)
  - Called automatically during `/spec-create` and `/spec-tasks` workflows
- **Enhanced Directory Structure**: Added `.claude/scripts/` directory for automation scripts
- **Improved Workflow Commands**: Updated `/spec-create` and `/spec-tasks` to generate task commands

### Changed
- **Command Organization**: Commands are now organized with main workflow commands at root level and spec-specific commands in subfolders
- **CLI Output**: Added mention of auto-generated task commands in setup success message
- **Documentation**: Updated README.md, CLAUDE.md, and CLI help to explain new command structure
- **Directory Structure**: Extended setup to create `.claude/scripts/` directory

### Enhanced
- **User Experience**: Easier task execution with shorter, more intuitive command names
- **Auto-Completion**: Claude Code can now suggest spec-specific commands
- **Organization**: Better command organization with clear separation between main workflow and task-specific commands
- **Backward Compatibility**: All existing commands continue to work unchanged

### Technical Details
- Added `src/scripts.ts` with command generation functionality
- Updated `src/setup.ts` to create scripts directory and files
- Enhanced `src/commands.ts` with instructions for command generation
- Updated `src/claude-md.ts` with new command documentation
- Added comprehensive tests for new functionality
- All 18 tests pass including new script creation tests

## [1.0.4] - 2025-07-19

### Changed
- Removed all "Kiro" references from codebase and replaced with "spec-driven" terminology
- Updated package description to use "spec-driven workflow" instead of "Kiro-style"
- Removed "kiro" keyword from package.json keywords array
- Updated source code comments and documentation to use consistent "spec-driven" language

### Technical Details
- Updated TypeScript source files: `src/commands.ts`, `src/claude-md.ts`
- Updated documentation: `README.md`, `CHANGELOG.md`
- Updated configuration: `package.json`, `.npmignore`
- Rebuilt all compiled JavaScript files in `dist/` directory
- Preserved Kiro acknowledgment in README.md as requested
- All tests continue to pass (17/17)
- Maintained full backward compatibility and functionality

## [1.0.3] - 2025-07-18

### Fixed
- Removed problematic self-dependency that was causing npm installation failures
- Fixed "ENOENT: no such file or directory" error when running `npx @pimzino/claude-code-spec-workflow`
- Eliminated circular dependency on `pimzino-claude-code-spec-workflow-1.0.1.tgz` file

### Technical Details
- Removed erroneous self-reference from package.json dependencies
- Removed leftover tarball file from project root directory
- Added `*.tgz` to .gitignore to prevent future tarball commits
- Package now installs cleanly without looking for non-existent tarball files
- Resolves npm cache corruption warnings during installation

## [1.0.2] - 2025-07-18

### Fixed
- Improved current CLAUDE.md handling to avoid overwriting existing content

## [1.0.1] - 2025-07-18

### Fixed
- Fixed NPX execution issue by adding package name as binary entry
- NPX command `npx @pimzino/claude-code-spec-workflow` now works correctly
- Added `claude-code-spec-workflow` binary alongside existing `claude-spec-setup`

### Technical Details
- Updated package.json bin configuration to include both binary names
- Ensures compatibility with NPX's expected binary naming convention

## [1.0.0] - 2025-07-18

### Added
- Initial release of Claude Code Spec Workflow
- Automated spec-driven workflow for Claude Code
- Complete Requirements → Design → Tasks → Implementation process
- 7 slash commands for workflow automation:
  - `/spec-create` - Create new feature specifications
  - `/spec-requirements` - Generate requirements documents
  - `/spec-design` - Create technical design documents
  - `/spec-tasks` - Generate implementation task lists
  - `/spec-execute` - Execute specific tasks
  - `/spec-status` - Show workflow status
  - `/spec-list` - List all specifications
- Interactive CLI setup with progress indicators
- Project type auto-detection (Node.js, Python, Java, etc.)
- Claude Code installation validation
- Comprehensive document templates
- EARS format requirements generation
- Mermaid diagram support in design documents
- Test-driven development focus
- Requirement traceability throughout workflow
- Professional TypeScript implementation
- Complete test suite with 17 tests
- Zero-configuration setup
- Smart file management (preserves existing CLAUDE.md)
- Cross-platform support (Windows, macOS, Linux)

### Features
- **Zero Configuration**: Works out of the box with any project
- **Interactive Setup**: Beautiful CLI with ora spinners and inquirer prompts
- **Smart File Management**: Preserves existing content while adding workflow
- **Professional Quality**: TypeScript, comprehensive error handling, npm best practices
- **Comprehensive Documentation**: Auto-generated CLAUDE.md with complete workflow instructions

### Technical Details
- Node.js 16.0.0+ requirement
- TypeScript implementation targeting ES2020
- Jest testing framework with ts-jest
- ESLint and Prettier for code quality
- Commander.js for CLI framework
- Inquirer for interactive prompts
- Ora for elegant terminal spinners
- Chalk for terminal styling

### Package Structure
```
.claude/
├── commands/           # 7 slash commands for spec workflow
├── templates/          # Document templates
├── specs/             # Generated spec files
└── agents/            # AI agents (enabled by default)
```

### Installation Options
- NPX (recommended): `npx @pimzino/claude-code-spec-workflow@latest`
- Global: `npm install -g @pimzino/claude-code-spec-workflow`
- Local: `npm install --save-dev @pimzino/claude-code-spec-workflow`

### Documentation
- Comprehensive README with examples
- Built-in test command
- Troubleshooting guide
- CI/CD integration examples
- Professional MIT license
