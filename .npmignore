# Source files
src/
tsconfig.json
.prettierrc
.eslintrc.js

# Development
.env
.env.*
*.test.ts
*.spec.ts
jest.config.js

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development dependencies
node_modules/

# Build artifacts
*.tsbuildinfo
coverage/

# Test files
__tests__/
test/

# Documentation development files
docs/
example_usage.py
test_setup.py
setup_spec_workflow.py