name: ✨ Feature Request
description: Suggest a new feature or enhancement for the Claude Code Spec Workflow
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a feature! Help us understand what you'd like to see.

  - type: textarea
    id: feature-description
    attributes:
      label: Feature Description
      description: What feature would you like to see?
      placeholder: Describe the feature you'd like...
    validations:
      required: true

  - type: textarea
    id: problem-statement
    attributes:
      label: Problem or Use Case
      description: What problem would this solve or what would you use it for?
      placeholder: |
        Describe the problem or use case:
        - What are you trying to accomplish?
        - What's currently difficult or missing?
    validations:
      required: true

  - type: textarea
    id: proposed-solution
    attributes:
      label: How should it work?
      description: Describe how you envision this feature working
      placeholder: How would you like this feature to work?

  - type: dropdown
    id: feature-area
    attributes:
      label: Feature Area
      description: Which area would this affect?
      options:
        - CLI Commands
        - Dashboard
        - Workflows (Spec/Bug)
        - Templates
        - Claude Code Integration
        - Other

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Anything else that would help us understand this request?
      placeholder: Add any other details, examples, or context here...