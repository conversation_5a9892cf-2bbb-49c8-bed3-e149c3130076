{"name": "@pimzino/claude-code-spec-workflow", "version": "1.5.8", "description": "Automated workflows for Claude Code. Includes spec-driven development (Requirements → Design → Tasks → Implementation) with intelligent task execution, optional steering documents and streamlined bug fix workflow (Report → Analyze → Fix → Verify). We have a bonus and option real time dashboard to monitor your specs and tasks.", "main": "dist/index.js", "bin": {"claude-code-spec-workflow": "dist/cli.js", "claude-spec-setup": "dist/cli.js", "claude-spec-dashboard": "dist/dashboard/cli.js"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tsc && npm run build:dashboard && npm run copy-static && npm run fix-permissions", "build:frontend": "npm run build:frontend:dev", "build:frontend:dev": "node esbuild.config.js development", "build:frontend:prod": "node esbuild.config.js production", "build:dashboard": "npm run typecheck:frontend && npm run build:frontend:prod", "watch:frontend": "node esbuild.config.js watch", "typecheck": "npm run typecheck:backend && npm run typecheck:frontend", "typecheck:backend": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "typecheck:frontend": "tsc --project src/dashboard/client/tsconfig.json --noEmit", "copy-static": "cpy 'src/dashboard/*.html' 'src/dashboard/*.svg' 'dist/dashboard/' && node scripts/copy-static.js", "fix-permissions": "node scripts/fix-permissions.js", "prepare": "npm run build", "dev": "ts-node src/cli.ts", "dev:dashboard": "concurrently \"npm run watch:frontend\" \"ts-node src/dashboard/cli.ts\" --names \"frontend,backend\" --prefix-colors \"blue,green\" --kill-others-on-fail", "dev:dashboard:backend-only": "ts-node src/dashboard/cli.ts", "dev:dashboard:tauri": "CLAUDE_SPEC_DEBUG=true ts-node src/dashboard/cli.ts --port 8247", "test": "jest", "test:patterns": "jest tests/pattern-consistency.test.ts", "lint": "eslint src --ext .ts --fix", "format": "prettier --write src"}, "keywords": ["claude-code", "spec-workflow", "bug-fix-workflow", "task-execution", "requirements", "design", "tasks", "implementation", "ai-development", "automation", "development-workflow", "bug-tracking", "issue-resolution"], "author": "Pimzin<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/pimzino/claude-code-spec-workflow.git"}, "bugs": {"url": "https://github.com/pimzino/claude-code-spec-workflow/issues"}, "homepage": "https://github.com/pimzino/claude-code-spec-workflow#readme", "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md", "docs", "examples"], "engines": {"node": ">=16.0.0"}, "dependencies": {"@fastify/static": "^7.0.4", "@fastify/websocket": "^8.2.1", "@ngrok/ngrok": "^1.4.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "cloudflared-tunnel": "^1.0.3", "commander": "^12.1.0", "fastify": "^4.24.3", "inquirer": "^8.2.6", "open": "^8.4.2", "ora": "^5.4.1", "simple-git": "^3.28.0"}, "devDependencies": {"@tauri-apps/cli": "^2.7.1", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "concurrently": "^9.2.0", "cpy-cli": "^5.0.0", "esbuild": "^0.25.8", "eslint": "^9.17.0", "eslint-plugin-import": "^2.32.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "publishConfig": {"access": "public"}}